<?php
require __DIR__ . '/vendor/autoload.php';

use Cashfree\Cashfree;
use Cashfree\Model\CreateOrderRequest;
use Cashfree\Model\CustomerDetails;

// Set credentials using the correct constructor for Cashfree class
$cashfree = new Cashfree(
    0, // 0 for SANDBOX, 1 for PRODUCTION
    "TEST100381645b8aef452253712db34946183001",
    "cfsk_ma_test_1f6cc35035884de1211e36c382031940_825c5fa1",
    "", // XPartnerApiKey (optional)
    "", // XPartnerMerchantId (optional)
    "", // XClientSignature (optional)
    true // XEnableErrorAnalytics (optional)
);

// Get form data
$amount = $_POST['amount'];
$name   = $_POST['name'];
$phone  = $_POST['phone'];

// Prepare order request
$request = new CreateOrderRequest();
$request->setOrderId("order_" . time());
$request->setOrderAmount((float)$amount);
$request->setOrderCurrency("INR");

$customer = new CustomerDetails();
$customer->setCustomerId("user_" . time());
$customer->setCustomerPhone($phone);
$customer->setCustomerName($name);
$request->setCustomerDetails($customer);

// Create the order
try {
    $responseArr = $cashfree->PGCreateOrder($request);
    $response = $responseArr[0];
    echo "<pre>";
    print_r($response);
    echo "</pre>";
    exit;
    // $paymentLink = $response['payment_session_id'] ?? null;
    // if ($paymentLink) {
    //     header("Location: https://payments.cashfree.com/pg/checkout?payment_session_id=" . $paymentLink);
    //     exit;
    // } else {
    //     echo "Failed to create payment session:<br>";
    //     print_r($response);
    // }
} catch (Exception $e) {
    echo "Exception while creating order: " . $e->getMessage();
}
