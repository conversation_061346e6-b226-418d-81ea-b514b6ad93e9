# PSR-7 Message Factory

[![Latest Version](https://img.shields.io/github/release/php-http/message-factory.svg?style=flat-square)](https://github.com/php-http/message-factory/releases)
[![Software License](https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat-square)](LICENSE)
[![Total Downloads](https://img.shields.io/packagist/dt/php-http/message-factory.svg?style=flat-square)](https://packagist.org/packages/php-http/message-factory)

**Factory interfaces for PSR-7 HTTP Message.**

## Obsolete

The PHP-HTTP factories have become obsolete with the [PSR-17](https://www.php-fig.org/psr/psr-17/) factories standard.
All major HTTP client implementors provide [PSR-17 factories](https://packagist.org/packages/psr/http-factory).

This package will remain available for the time being to not break legacy code, but we encourage everybody to move to PSR-17.

## Install

Via Composer

``` bash
$ composer require php-http/message-factory
```


## Documentation

Please see the [official documentation](http://docs.php-http.org/en/latest/message/message-factory.html).


## Contributing

Please see our [contributing guide](http://docs.php-http.org/en/latest/development/contributing.html).


## Security

If you discover any security related issues, please contact us at [<EMAIL>](mailto:<EMAIL>).


## License

The MIT License (MIT). Please see [License File](LICENSE) for more information.
