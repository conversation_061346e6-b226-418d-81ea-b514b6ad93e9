# HTTP Client Common

[![Latest Version](https://img.shields.io/github/release/php-http/client-common.svg?style=flat-square)](https://github.com/php-http/client-common/releases)
[![Software License](https://img.shields.io/badge/license-MIT-brightgreen.svg?style=flat-square)](LICENSE)
[![Build Status](https://github.com/php-http/client-common/actions/workflows/tests.yml/badge.svg)](https://github.com/php-http/client-common/actions/workflows/tests.yml)
[![Code Coverage](https://img.shields.io/scrutinizer/coverage/g/php-http/client-common.svg?style=flat-square)](https://scrutinizer-ci.com/g/php-http/client-common)
[![Quality Score](https://img.shields.io/scrutinizer/g/php-http/client-common.svg?style=flat-square)](https://scrutinizer-ci.com/g/php-http/client-common)
[![Total Downloads](https://img.shields.io/packagist/dt/php-http/client-common.svg?style=flat-square)](https://packagist.org/packages/php-http/client-common)

**Common HTTP Client implementations and tools for HTTPlug.**


## Install

Via Composer

``` bash
$ composer require php-http/client-common
```


## Usage

This package provides common tools for HTTP Clients:

- BatchClient to handle sending requests in parallel
- A convenience client with HTTP method names as class methods
- Emulator, decorator layers for sync/async clients


## Documentation

Please see the [official documentation](http://docs.php-http.org/en/latest/components/client-common.html).


## Testing

``` bash
$ composer test
```


## Contributing

Please see our [contributing guide](http://docs.php-http.org/en/latest/development/contributing.html).


## Security

If you discover any security related issues, please contact us at [<EMAIL>](mailto:<EMAIL>).


## License

The MIT License (MIT). Please see [License File](LICENSE) for more information.
