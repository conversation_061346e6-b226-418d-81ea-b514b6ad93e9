<?php
/**
 * OfferValidationsPaymentMethod
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2023-08-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * OfferValidationsPaymentMethod Class Doc Comment
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class OfferValidationsPaymentMethod implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'OfferValidations_payment_method';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'all' => 'object',
        'card' => '\Cashfree\Model\CardOffer',
        'netbanking' => '\Cashfree\Model\OfferNBNetbanking',
        'app' => '\Cashfree\Model\WalletOffer',
        'upi' => 'object',
        'paylater' => '\Cashfree\Model\PaylaterOffer',
        'emi' => '\Cashfree\Model\EMIOffer'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'all' => null,
        'card' => null,
        'netbanking' => null,
        'app' => null,
        'upi' => null,
        'paylater' => null,
        'emi' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'all' => false,
		'card' => false,
		'netbanking' => false,
		'app' => false,
		'upi' => false,
		'paylater' => false,
		'emi' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'all' => 'all',
        'card' => 'card',
        'netbanking' => 'netbanking',
        'app' => 'app',
        'upi' => 'upi',
        'paylater' => 'paylater',
        'emi' => 'emi'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'all' => 'setAll',
        'card' => 'setCard',
        'netbanking' => 'setNetbanking',
        'app' => 'setApp',
        'upi' => 'setUpi',
        'paylater' => 'setPaylater',
        'emi' => 'setEmi'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'all' => 'getAll',
        'card' => 'getCard',
        'netbanking' => 'getNetbanking',
        'app' => 'getApp',
        'upi' => 'getUpi',
        'paylater' => 'getPaylater',
        'emi' => 'getEmi'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('all', $data ?? [], null);
        $this->setIfExists('card', $data ?? [], null);
        $this->setIfExists('netbanking', $data ?? [], null);
        $this->setIfExists('app', $data ?? [], null);
        $this->setIfExists('upi', $data ?? [], null);
        $this->setIfExists('paylater', $data ?? [], null);
        $this->setIfExists('emi', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        if ($this->container['all'] === null) {
            $invalidProperties[] = "'all' can't be null";
        }
        if ($this->container['card'] === null) {
            $invalidProperties[] = "'card' can't be null";
        }
        if ($this->container['netbanking'] === null) {
            $invalidProperties[] = "'netbanking' can't be null";
        }
        if ($this->container['app'] === null) {
            $invalidProperties[] = "'app' can't be null";
        }
        if ($this->container['upi'] === null) {
            $invalidProperties[] = "'upi' can't be null";
        }
        if ($this->container['paylater'] === null) {
            $invalidProperties[] = "'paylater' can't be null";
        }
        if ($this->container['emi'] === null) {
            $invalidProperties[] = "'emi' can't be null";
        }
        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets all
     *
     * @return object
     */
    public function getAll()
    {
        return $this->container['all'];
    }

    /**
     * Sets all
     *
     * @param object $all All offers applicable
     *
     * @return self
     */
    public function setAll($all)
    {
        if (is_null($all)) {
            throw new \InvalidArgumentException('non-nullable all cannot be null');
        }
        $this->container['all'] = $all;

        return $this;
    }

    /**
     * Gets card
     *
     * @return \Cashfree\Model\CardOffer
     */
    public function getCard()
    {
        return $this->container['card'];
    }

    /**
     * Sets card
     *
     * @param \Cashfree\Model\CardOffer $card card
     *
     * @return self
     */
    public function setCard($card)
    {
        if (is_null($card)) {
            throw new \InvalidArgumentException('non-nullable card cannot be null');
        }
        $this->container['card'] = $card;

        return $this;
    }

    /**
     * Gets netbanking
     *
     * @return \Cashfree\Model\OfferNBNetbanking
     */
    public function getNetbanking()
    {
        return $this->container['netbanking'];
    }

    /**
     * Sets netbanking
     *
     * @param \Cashfree\Model\OfferNBNetbanking $netbanking netbanking
     *
     * @return self
     */
    public function setNetbanking($netbanking)
    {
        if (is_null($netbanking)) {
            throw new \InvalidArgumentException('non-nullable netbanking cannot be null');
        }
        $this->container['netbanking'] = $netbanking;

        return $this;
    }

    /**
     * Gets app
     *
     * @return \Cashfree\Model\WalletOffer
     */
    public function getApp()
    {
        return $this->container['app'];
    }

    /**
     * Sets app
     *
     * @param \Cashfree\Model\WalletOffer $app app
     *
     * @return self
     */
    public function setApp($app)
    {
        if (is_null($app)) {
            throw new \InvalidArgumentException('non-nullable app cannot be null');
        }
        $this->container['app'] = $app;

        return $this;
    }

    /**
     * Gets upi
     *
     * @return object
     */
    public function getUpi()
    {
        return $this->container['upi'];
    }

    /**
     * Sets upi
     *
     * @param object $upi upi
     *
     * @return self
     */
    public function setUpi($upi)
    {
        if (is_null($upi)) {
            throw new \InvalidArgumentException('non-nullable upi cannot be null');
        }
        $this->container['upi'] = $upi;

        return $this;
    }

    /**
     * Gets paylater
     *
     * @return \Cashfree\Model\PaylaterOffer
     */
    public function getPaylater()
    {
        return $this->container['paylater'];
    }

    /**
     * Sets paylater
     *
     * @param \Cashfree\Model\PaylaterOffer $paylater paylater
     *
     * @return self
     */
    public function setPaylater($paylater)
    {
        if (is_null($paylater)) {
            throw new \InvalidArgumentException('non-nullable paylater cannot be null');
        }
        $this->container['paylater'] = $paylater;

        return $this;
    }

    /**
     * Gets emi
     *
     * @return \Cashfree\Model\EMIOffer
     */
    public function getEmi()
    {
        return $this->container['emi'];
    }

    /**
     * Sets emi
     *
     * @param \Cashfree\Model\EMIOffer $emi emi
     *
     * @return self
     */
    public function setEmi($emi)
    {
        if (is_null($emi)) {
            throw new \InvalidArgumentException('non-nullable emi cannot be null');
        }
        $this->container['emi'] = $emi;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


