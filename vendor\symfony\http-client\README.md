HttpClient component
====================

The HttpClient component provides powerful methods to fetch HTTP resources synchronously or asynchronously.

Sponsor
-------

The HttpClient component for Symfony 6.4 is [backed][1] by [Innovative Web AG][2].

Innovative Web AG (i-web) is a specialist for web, applications and the
digitalisation of the public sector based in Switzerland. With their i-CMS,
public authorities and institutions implement modern websites and eGovernment
portals and offer user-friendly eServices for residents and companies.

Help Symfony by [sponsoring][3] its development!

Resources
---------

 * [Documentation](https://symfony.com/doc/current/components/http_client.html)
 * [Contributing](https://symfony.com/doc/current/contributing/index.html)
 * [Report issues](https://github.com/symfony/symfony/issues) and
   [send Pull Requests](https://github.com/symfony/symfony/pulls)
   in the [main Symfony repository](https://github.com/symfony/symfony)

[1]: https://symfony.com/backers
[2]: https://www.i-web.ch
[3]: https://symfony.com/sponsor
