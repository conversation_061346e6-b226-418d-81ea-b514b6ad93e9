<?php
/**
 * LinkCustomerDetailsEntityTest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2025-01-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the model.
 */

namespace Cashfree\Test\Model;

use PHPUnit\Framework\TestCase;

/**
 * LinkCustomerDetailsEntityTest Class Doc Comment
 *
 * @category    Class
 * @description Payment link customer entity
 * @package     Cashfree
 * <AUTHOR> Generator team
 * @link        https://openapi-generator.tech
 */
class LinkCustomerDetailsEntityTest extends TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test "LinkCustomerDetailsEntity"
     */
    public function testLinkCustomerDetailsEntity()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_phone"
     */
    public function testPropertyCustomerPhone()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_email"
     */
    public function testPropertyCustomerEmail()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_name"
     */
    public function testPropertyCustomerName()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_bank_account_number"
     */
    public function testPropertyCustomerBankAccountNumber()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_bank_ifsc"
     */
    public function testPropertyCustomerBankIfsc()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_bank_code"
     */
    public function testPropertyCustomerBankCode()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }
}
