<?php

namespace {

    /** Real (wall-clock) time */
    define( 'EXCIMER_REAL', 0 );

    /** CPU time (user and system) consumed by the thread during execution */
    define( 'EXCIMER_CPU', 1 );

    /**
     * Abbreviated interface for starting a wall-clock timer. Equivalent to:
     *
     *   $timer = new ExcimerTimer;
     *   $timer->setCallback( $callback );
     *   $timer->setInterval( $interval );
     *   $timer->start();
     *   return $timer;
     *
     * Note that you must keep a copy of the return value. If it goes out of scope,
     * the object will be destroyed and the timer will stop.
     *
     * If the callback is not callable, a warning is raised and null is returned.
     *
     * @param callable $callback
     * @param float $interval
     * @return ExcimerTimer|null
     */
    function excimer_set_timeout( $callback, $interval ) {
    }

}
