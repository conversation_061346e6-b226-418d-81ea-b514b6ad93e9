<?php

namespace {

    /**
     * Generic timer class. Calls a callback after a specified time has elapsed, or
     * periodically with a given interval.
     */
    class ExcimerTimer
    {
        /**
         * Set the type of time used by this object. May be either EXCIMER_REAL for
         * real (wall-clock) time, or EXCIMER_CPU for CPU time. If this function is
         * not called, EXCIMER_REAL will be used.
         *
         * @param int $eventType
         * @return void
         */
        public function setEventType($eventType)
        {
        }

        /**
         * Switch to one-shot mode, and set the interval. This will take effect
         * when start() is next called.
         *
         * @param float $interval The interval in seconds
         * @return void
         */
        public function setInterval($interval)
        {
        }

        /**
         * Switch to periodic mode, and set the period. This will take effect when
         * start() is next called.
         *
         * @param float $period The period in seconds.
         * @return void
         */
        public function setPeriod($period)
        {
        }

        /**
         * Set the callback function, to be called next time either a one-shot
         * or periodic event occurs.
         *
         * The callback function shall take one parameter: an integer representing
         * the number of periods which have elapsed since the callback was last
         * called. This may be greater than 1 for two reasons:
         *
         *   - The kernel or C library may fail to respond to the event in time,
         *     and so may increment an overrun counter.
         *
         *   - The native callback may be called multiple times before the PHP VM
         *     has the chance to interrupt execution. For example, a long-running
         *     native function such as a database connect will not be interrupted
         *     when the timer is triggered.
         *
         * If the callback is set to null, or if this function has not been called,
         * no action is taken when the event occurs.
         *
         * @param callable|null $callback
         * @return void
         */
        public function setCallback($callback)
        {
        }

        /**
         * Start the timer.
         *
         * If the timer is already running, it will be stopped and restarted,
         * respecting any changes to the interval, period or event type.
         *
         * @return void
         */
        public function start()
        {
        }

        /**
         * Stop the timer.
         *
         * If the timer is not already running, this will have no effect.
         *
         * @return void
         */
        public function stop()
        {
        }

        /**
         * Get the time until the next expiration, in seconds. If this is zero,
         * the timer is currently not running.
         *
         * @return float
         */
        public function getTime()
        {
        }
    }

}
