{"name": "php-http/message", "description": "HTTP Message related tools", "keywords": ["message", "http", "psr-7"], "homepage": "http://php-http.org", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.2 || ^8.0", "clue/stream-filter": "^1.5", "psr/http-message": "^1.1 || ^2.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "ergebnis/composer-normalize": "^2.6", "guzzlehttp/psr7": "^1.0 || ^2.0", "php-http/message-factory": "^1.0.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0", "laminas/laminas-diactoros": "^2.0 || ^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "config": {"sort-packages": true, "allow-plugins": {"ergebnis/composer-normalize": true}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}, "files": ["src/filters.php"]}, "autoload-dev": {"psr-4": {"spec\\Http\\Message\\": "spec/"}}, "scripts": {"test": "vendor/bin/phpspec run", "test-ci": "vendor/bin/phpspec run -c phpspec.ci.yml"}}