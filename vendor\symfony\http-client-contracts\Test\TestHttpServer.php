<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Contracts\HttpClient\Test;

use Symfony\Component\Process\PhpExecutableFinder;
use Symfony\Component\Process\Process;

class TestHttpServer
{
    private static array $process = [];

    /**
     * @param string|null $workingDirectory
     */
    public static function start(int $port = 8057/* , ?string $workingDirectory = null */): Process
    {
        $workingDirectory = \func_get_args()[1] ?? __DIR__.'/Fixtures/web';

        if (0 > $port) {
            $port = -$port;
            $ip = '[::1]';
        } else {
            $ip = '127.0.0.1';
        }

        if (isset(self::$process[$port])) {
            self::$process[$port]->stop();
        } else {
            register_shutdown_function(static function () use ($port) {
                self::$process[$port]->stop();
            });
        }

        $finder = new PhpExecutableFinder();
        $process = new Process(array_merge([$finder->find(false)], $finder->findArguments(), ['-dopcache.enable=0', '-dvariables_order=EGPCS', '-S', $ip.':'.$port]));
        $process->setWorkingDirectory($workingDirectory);
        $process->start();
        self::$process[$port] = $process;

        do {
            usleep(50000);
        } while (!@fopen('http://'.$ip.':'.$port, 'r'));

        return $process;
    }

    public static function stop(int $port = 8057)
    {
        if (isset(self::$process[$port])) {
            self::$process[$port]->stop();
        }
    }
}
