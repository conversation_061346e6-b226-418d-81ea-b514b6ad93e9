<?php
/**
 * PaymentMethodInPaymentsEntityPaymentMethod
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2022-09-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Cashfree\Model;

use \ArrayAccess;
use \Cashfree\ObjectSerializer;

/**
 * PaymentMethodInPaymentsEntityPaymentMethod Class Doc Comment
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 * @implements \ArrayAccess<string, mixed>
 */
class PaymentMethodInPaymentsEntityPaymentMethod implements ModelInterface, ArrayAccess, \JsonSerializable
{
    public const DISCRIMINATOR = null;

    /**
      * The original name of the model.
      *
      * @var string
      */
    protected static $openAPIModelName = 'PaymentMethodInPaymentsEntity_payment_method';

    /**
      * Array of property to type mappings. Used for (de)serialization
      *
      * @var string[]
      */
    protected static $openAPITypes = [
        'card' => '\Cashfree\Model\PaymentMethodCardInPaymentsEntityCard',
        'netbanking' => '\Cashfree\Model\PaymentMethodNetBankingInPaymentsEntityNetbanking',
        'upi' => '\Cashfree\Model\PaymentMethodUPIInPaymentsEntityUpi',
        'app' => '\Cashfree\Model\PaymentMethodAppInPaymentsEntityApp',
        'cardless_emi' => '\Cashfree\Model\PaymentMethodAppInPaymentsEntityApp',
        'paylater' => '\Cashfree\Model\PaymentMethodAppInPaymentsEntityApp',
        'emi' => '\Cashfree\Model\PaymentMethodCardEMIInPaymentsEntityEmi'
    ];

    /**
      * Array of property to format mappings. Used for (de)serialization
      *
      * @var string[]
      * @phpstan-var array<string, string|null>
      * @psalm-var array<string, string|null>
      */
    protected static $openAPIFormats = [
        'card' => null,
        'netbanking' => null,
        'upi' => null,
        'app' => null,
        'cardless_emi' => null,
        'paylater' => null,
        'emi' => null
    ];

    /**
      * Array of nullable properties. Used for (de)serialization
      *
      * @var boolean[]
      */
    protected static $openAPINullables = [
        'card' => false,
		'netbanking' => false,
		'upi' => false,
		'app' => false,
		'cardless_emi' => false,
		'paylater' => false,
		'emi' => false
    ];

    /**
      * If a nullable field gets set to null, insert it here
      *
      * @var boolean[]
      */
    protected $openAPINullablesSetToNull = [];

    /**
     * Array of property to type mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPITypes()
    {
        return self::$openAPITypes;
    }

    /**
     * Array of property to format mappings. Used for (de)serialization
     *
     * @return array
     */
    public static function openAPIFormats()
    {
        return self::$openAPIFormats;
    }

    /**
     * Array of nullable properties
     *
     * @return array
     */
    protected static function openAPINullables(): array
    {
        return self::$openAPINullables;
    }

    /**
     * Array of nullable field names deliberately set to null
     *
     * @return boolean[]
     */
    private function getOpenAPINullablesSetToNull(): array
    {
        return $this->openAPINullablesSetToNull;
    }

    /**
     * Setter - Array of nullable field names deliberately set to null
     *
     * @param boolean[] $openAPINullablesSetToNull
     */
    private function setOpenAPINullablesSetToNull(array $openAPINullablesSetToNull): void
    {
        $this->openAPINullablesSetToNull = $openAPINullablesSetToNull;
    }

    /**
     * Checks if a property is nullable
     *
     * @param string $property
     * @return bool
     */
    public static function isNullable(string $property): bool
    {
        return self::openAPINullables()[$property] ?? false;
    }

    /**
     * Checks if a nullable property is set to null.
     *
     * @param string $property
     * @return bool
     */
    public function isNullableSetToNull(string $property): bool
    {
        return in_array($property, $this->getOpenAPINullablesSetToNull(), true);
    }

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @var string[]
     */
    protected static $attributeMap = [
        'card' => 'card',
        'netbanking' => 'netbanking',
        'upi' => 'upi',
        'app' => 'app',
        'cardless_emi' => 'cardless_emi',
        'paylater' => 'paylater',
        'emi' => 'emi'
    ];

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @var string[]
     */
    protected static $setters = [
        'card' => 'setCard',
        'netbanking' => 'setNetbanking',
        'upi' => 'setUpi',
        'app' => 'setApp',
        'cardless_emi' => 'setCardlessEmi',
        'paylater' => 'setPaylater',
        'emi' => 'setEmi'
    ];

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @var string[]
     */
    protected static $getters = [
        'card' => 'getCard',
        'netbanking' => 'getNetbanking',
        'upi' => 'getUpi',
        'app' => 'getApp',
        'cardless_emi' => 'getCardlessEmi',
        'paylater' => 'getPaylater',
        'emi' => 'getEmi'
    ];

    /**
     * Array of attributes where the key is the local name,
     * and the value is the original name
     *
     * @return array
     */
    public static function attributeMap()
    {
        return self::$attributeMap;
    }

    /**
     * Array of attributes to setter functions (for deserialization of responses)
     *
     * @return array
     */
    public static function setters()
    {
        return self::$setters;
    }

    /**
     * Array of attributes to getter functions (for serialization of requests)
     *
     * @return array
     */
    public static function getters()
    {
        return self::$getters;
    }

    /**
     * The original name of the model.
     *
     * @return string
     */
    public function getModelName()
    {
        return self::$openAPIModelName;
    }


    /**
     * Associative array for storing property values
     *
     * @var mixed[]
     */
    protected $container = [];

    /**
     * Constructor
     *
     * @param mixed[] $data Associated array of property values
     *                      initializing the model
     */
    public function __construct(array $data = null)
    {
        $this->setIfExists('card', $data ?? [], null);
        $this->setIfExists('netbanking', $data ?? [], null);
        $this->setIfExists('upi', $data ?? [], null);
        $this->setIfExists('app', $data ?? [], null);
        $this->setIfExists('cardless_emi', $data ?? [], null);
        $this->setIfExists('paylater', $data ?? [], null);
        $this->setIfExists('emi', $data ?? [], null);
    }

    /**
    * Sets $this->container[$variableName] to the given data or to the given default Value; if $variableName
    * is nullable and its value is set to null in the $fields array, then mark it as "set to null" in the
    * $this->openAPINullablesSetToNull array
    *
    * @param string $variableName
    * @param array  $fields
    * @param mixed  $defaultValue
    */
    private function setIfExists(string $variableName, array $fields, $defaultValue): void
    {
        if (self::isNullable($variableName) && array_key_exists($variableName, $fields) && is_null($fields[$variableName])) {
            $this->openAPINullablesSetToNull[] = $variableName;
        }

        $this->container[$variableName] = $fields[$variableName] ?? $defaultValue;
    }

    /**
     * Show all the invalid properties with reasons.
     *
     * @return array invalid properties with reasons
     */
    public function listInvalidProperties()
    {
        $invalidProperties = [];

        return $invalidProperties;
    }

    /**
     * Validate all the properties in the model
     * return true if all passed
     *
     * @return bool True if all properties are valid
     */
    public function valid()
    {
        return count($this->listInvalidProperties()) === 0;
    }


    /**
     * Gets card
     *
     * @return \Cashfree\Model\PaymentMethodCardInPaymentsEntityCard|null
     */
    public function getCard()
    {
        return $this->container['card'];
    }

    /**
     * Sets card
     *
     * @param \Cashfree\Model\PaymentMethodCardInPaymentsEntityCard|null $card card
     *
     * @return self
     */
    public function setCard($card)
    {
        if (is_null($card)) {
            throw new \InvalidArgumentException('non-nullable card cannot be null');
        }
        $this->container['card'] = $card;

        return $this;
    }

    /**
     * Gets netbanking
     *
     * @return \Cashfree\Model\PaymentMethodNetBankingInPaymentsEntityNetbanking|null
     */
    public function getNetbanking()
    {
        return $this->container['netbanking'];
    }

    /**
     * Sets netbanking
     *
     * @param \Cashfree\Model\PaymentMethodNetBankingInPaymentsEntityNetbanking|null $netbanking netbanking
     *
     * @return self
     */
    public function setNetbanking($netbanking)
    {
        if (is_null($netbanking)) {
            throw new \InvalidArgumentException('non-nullable netbanking cannot be null');
        }
        $this->container['netbanking'] = $netbanking;

        return $this;
    }

    /**
     * Gets upi
     *
     * @return \Cashfree\Model\PaymentMethodUPIInPaymentsEntityUpi|null
     */
    public function getUpi()
    {
        return $this->container['upi'];
    }

    /**
     * Sets upi
     *
     * @param \Cashfree\Model\PaymentMethodUPIInPaymentsEntityUpi|null $upi upi
     *
     * @return self
     */
    public function setUpi($upi)
    {
        if (is_null($upi)) {
            throw new \InvalidArgumentException('non-nullable upi cannot be null');
        }
        $this->container['upi'] = $upi;

        return $this;
    }

    /**
     * Gets app
     *
     * @return \Cashfree\Model\PaymentMethodAppInPaymentsEntityApp|null
     */
    public function getApp()
    {
        return $this->container['app'];
    }

    /**
     * Sets app
     *
     * @param \Cashfree\Model\PaymentMethodAppInPaymentsEntityApp|null $app app
     *
     * @return self
     */
    public function setApp($app)
    {
        if (is_null($app)) {
            throw new \InvalidArgumentException('non-nullable app cannot be null');
        }
        $this->container['app'] = $app;

        return $this;
    }

    /**
     * Gets cardless_emi
     *
     * @return \Cashfree\Model\PaymentMethodAppInPaymentsEntityApp|null
     */
    public function getCardlessEmi()
    {
        return $this->container['cardless_emi'];
    }

    /**
     * Sets cardless_emi
     *
     * @param \Cashfree\Model\PaymentMethodAppInPaymentsEntityApp|null $cardless_emi cardless_emi
     *
     * @return self
     */
    public function setCardlessEmi($cardless_emi)
    {
        if (is_null($cardless_emi)) {
            throw new \InvalidArgumentException('non-nullable cardless_emi cannot be null');
        }
        $this->container['cardless_emi'] = $cardless_emi;

        return $this;
    }

    /**
     * Gets paylater
     *
     * @return \Cashfree\Model\PaymentMethodAppInPaymentsEntityApp|null
     */
    public function getPaylater()
    {
        return $this->container['paylater'];
    }

    /**
     * Sets paylater
     *
     * @param \Cashfree\Model\PaymentMethodAppInPaymentsEntityApp|null $paylater paylater
     *
     * @return self
     */
    public function setPaylater($paylater)
    {
        if (is_null($paylater)) {
            throw new \InvalidArgumentException('non-nullable paylater cannot be null');
        }
        $this->container['paylater'] = $paylater;

        return $this;
    }

    /**
     * Gets emi
     *
     * @return \Cashfree\Model\PaymentMethodCardEMIInPaymentsEntityEmi|null
     */
    public function getEmi()
    {
        return $this->container['emi'];
    }

    /**
     * Sets emi
     *
     * @param \Cashfree\Model\PaymentMethodCardEMIInPaymentsEntityEmi|null $emi emi
     *
     * @return self
     */
    public function setEmi($emi)
    {
        if (is_null($emi)) {
            throw new \InvalidArgumentException('non-nullable emi cannot be null');
        }
        $this->container['emi'] = $emi;

        return $this;
    }
    /**
     * Returns true if offset exists. False otherwise.
     *
     * @param integer $offset Offset
     *
     * @return boolean
     */
    public function offsetExists($offset): bool
    {
        return isset($this->container[$offset]);
    }

    /**
     * Gets offset.
     *
     * @param integer $offset Offset
     *
     * @return mixed|null
     */
    #[\ReturnTypeWillChange]
    public function offsetGet($offset)
    {
        return $this->container[$offset] ?? null;
    }

    /**
     * Sets value based on offset.
     *
     * @param int|null $offset Offset
     * @param mixed    $value  Value to be set
     *
     * @return void
     */
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->container[] = $value;
        } else {
            $this->container[$offset] = $value;
        }
    }

    /**
     * Unsets offset.
     *
     * @param integer $offset Offset
     *
     * @return void
     */
    public function offsetUnset($offset): void
    {
        unset($this->container[$offset]);
    }

    /**
     * Serializes the object to a value that can be serialized natively by json_encode().
     * @link https://www.php.net/manual/en/jsonserializable.jsonserialize.php
     *
     * @return mixed Returns data which can be serialized by json_encode(), which is a value
     * of any type other than a resource.
     */
    #[\ReturnTypeWillChange]
    public function jsonSerialize()
    {
       return ObjectSerializer::sanitizeForSerialization($this);
    }

    /**
     * Gets the string presentation of the object
     *
     * @return string
     */
    public function __toString()
    {
        return json_encode(
            ObjectSerializer::sanitizeForSerialization($this),
            JSON_PRETTY_PRINT
        );
    }

    /**
     * Gets a header-safe presentation of the object
     *
     * @return string
     */
    public function toHeaderValue()
    {
        return json_encode(ObjectSerializer::sanitizeForSerialization($this));
    }
}


