<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cashfree/cashfree-pg' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => '94e8548bfae59ed7a4c84801b060ea8bb1974094',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cashfree/cashfree-pg',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'clue/stream-filter' => array(
            'pretty_version' => 'v1.7.0',
            'version' => '1.7.0.0',
            'reference' => '049509fef80032cb3f051595029ab75b49a3c2f7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../clue/stream-filter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '7c69f28996b0a6920945dd20b3857e499d9ca96c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'http-interop/http-factory-guzzle' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'reference' => '8f06e92b95405216b237521cc64c804dd44c4a81',
            'type' => 'library',
            'install_path' => __DIR__ . '/../http-interop/http-factory-guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jean85/pretty-package-versions' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '4d7aa5dab42e2a76d99559706022885de0e18e1a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jean85/pretty-package-versions',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/client-common' => array(
            'pretty_version' => '2.7.2',
            'version' => '2.7.2.0',
            'reference' => '0cfe9858ab9d3b213041b947c881d5b19ceeca46',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/client-common',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/discovery' => array(
            'pretty_version' => '1.20.0',
            'version' => '1.20.0.0',
            'reference' => '82fe4c73ef3363caed49ff8dd1539ba06044910d',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../php-http/discovery',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/httplug' => array(
            'pretty_version' => '2.4.1',
            'version' => '2.4.1.0',
            'reference' => '5cad731844891a4c282f3f3e1b582c46839d22f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/httplug',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message' => array(
            'pretty_version' => '1.16.2',
            'version' => '1.16.2.0',
            'reference' => '06dd5e8562f84e641bf929bfe699ee0f5ce8080a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '4d8778e1c7d405cbb471574821c1ff5b68cc8f57',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/message-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/message-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'php-http/promise' => array(
            'pretty_version' => '1.3.1',
            'version' => '1.3.1.0',
            'reference' => 'fc85b1fba37c169a69a07ef0d5a8075770cc1f83',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/promise',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '^1.0',
                2 => '*',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sentry/sdk' => array(
            'pretty_version' => '3.6.0',
            'version' => '3.6.0.0',
            'reference' => '24c235ff2027401cbea099bf88689e1a1f197c7a',
            'type' => 'metapackage',
            'install_path' => null,
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sentry/sentry' => array(
            'pretty_version' => '3.22.1',
            'version' => '3.22.1.0',
            'reference' => '8859631ba5ab15bc1af420b0eeed19ecc6c9d81d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sentry/sentry',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '63afe740e99a13ba87ec199bb07bbdee937a5b62',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client' => array(
            'pretty_version' => 'v6.4.23',
            'version' => '6.4.23.0',
            'reference' => '19f11e742b94dcfd968a54f5381bb9082a88cb57',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => '75d7043853a42837e68111812f4d964b01e5101c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-client-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '3.0',
            ),
        ),
        'symfony/options-resolver' => array(
            'pretty_version' => 'v6.4.16',
            'version' => '6.4.16.0',
            'reference' => '368128ad168f20e22c32159b9f761e456cec0c78',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/options-resolver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '0cc9dd0f17f61d8131e7df6b84bd344899fe2608',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.6.0',
            'version' => '3.6.0.0',
            'reference' => 'f021b05a130d35510bd6b25fe9053c2a8a15d5d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
