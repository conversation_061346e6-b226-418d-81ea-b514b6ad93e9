<?php
/**
 * CreateSubscriptionRequestTest
 *
 * PHP version 7.4
 *
 * @category Class
 * @package  Cashfree
 * <AUTHOR> Generator team
 * @link     https://openapi-generator.tech
 */

/**
 * Cashfree Payment Gateway APIs
 *
 * Cashfree's Payment Gateway APIs provide developers with a streamlined pathway to integrate advanced payment processing capabilities into their applications, platforms and websites.
 *
 * The version of the OpenAPI document: 2025-01-01
 * Contact: <EMAIL>
 * Generated by: https://openapi-generator.tech
 * OpenAPI Generator version: 7.0.0
 */

/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Please update the test case below to test the model.
 */

namespace Cashfree\Test\Model;

use PHPUnit\Framework\TestCase;

/**
 * CreateSubscriptionRequestTest Class Doc Comment
 *
 * @category    Class
 * @description Request body to create a new subscription.
 * @package     Cashfree
 * <AUTHOR> Generator team
 * @link        https://openapi-generator.tech
 */
class CreateSubscriptionRequestTest extends TestCase
{

    /**
     * Setup before running any test case
     */
    public static function setUpBeforeClass(): void
    {
    }

    /**
     * Setup before running each test case
     */
    public function setUp(): void
    {
    }

    /**
     * Clean up after running each test case
     */
    public function tearDown(): void
    {
    }

    /**
     * Clean up after running all test cases
     */
    public static function tearDownAfterClass(): void
    {
    }

    /**
     * Test "CreateSubscriptionRequest"
     */
    public function testCreateSubscriptionRequest()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "subscription_id"
     */
    public function testPropertySubscriptionId()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "customer_details"
     */
    public function testPropertyCustomerDetails()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "plan_details"
     */
    public function testPropertyPlanDetails()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "authorization_details"
     */
    public function testPropertyAuthorizationDetails()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "subscription_meta"
     */
    public function testPropertySubscriptionMeta()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "subscription_expiry_time"
     */
    public function testPropertySubscriptionExpiryTime()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "subscription_first_charge_time"
     */
    public function testPropertySubscriptionFirstChargeTime()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "subscription_note"
     */
    public function testPropertySubscriptionNote()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "subscription_tags"
     */
    public function testPropertySubscriptionTags()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }

    /**
     * Test attribute "subscription_payment_splits"
     */
    public function testPropertySubscriptionPaymentSplits()
    {
        // TODO: implement
        $this->markTestIncomplete('Not implemented');
    }
}
